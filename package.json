{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@vercel/analytics": "^1.5.0", "autoprefixer": "10.4.20", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "echarts": "^5.6.0", "google-auth-library": "^9.15.1", "lucide-react": "^0.468.0", "marked": "^15.0.12", "next": "latest", "next-themes": "^0.4.3", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/marked": "^5.0.2", "@types/node": "22.10.2", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "postcss": "8.4.49", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}}