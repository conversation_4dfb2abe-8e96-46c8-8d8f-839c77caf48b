import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('user_id');
    const startDate = searchParams.get('start_date');
    const endDate = searchParams.get('end_date');
    const groupBy = searchParams.get('group_by') || 'day';



    if (!userId) {
      return NextResponse.json(
        { error: 'user_id é obrigatório' },
        { status: 400 }
      );
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY!
    );

    // Buscar dados da tabela agent_interaction_costs
    let query = supabase
      .schema('app')
      .from('agent_interaction_costs')
      .select('*')
      .eq('user_id', userId)
      .order('response_timestamp', { ascending: true });

    // Aplicar filtros de data (considerando timezone de São Paulo)
    if (startDate) {
      // Início do dia em São Paulo: 00:00:00 -03:00
      const startDateTime = startDate + 'T00:00:00-03:00';
      query = query.gte('response_timestamp', startDateTime);

    }
    if (endDate) {
      // Final do dia em São Paulo: 23:59:59 -03:00
      const endDateTime = endDate + 'T23:59:59-03:00';
      query = query.lte('response_timestamp', endDateTime);

    }

    const { data, error } = await query;

    console.log('🔍 Consulta SQL executada:', {
      userId,
      startDate,
      endDate,
      groupBy,
      resultCount: data?.length || 0
    });

    if (error) {
      console.error('Erro ao buscar dados de Uso:', error);
      return NextResponse.json(
        { error: 'Erro ao buscar dados' },
        { status: 500 }
      );
    }
    // Log dos primeiros registros para debug
    if (data && data.length > 0) {
      console.log('📊 Primeiros registros encontrados:', data.slice(0, 3).map(item => ({
        date: item.response_timestamp,
        invocation_id: item.invocation_id,
        author: item.author
      })));
    }





    if (!data || data.length === 0) {
      return NextResponse.json({
        chartData: [],
        totals: {
          total_tokens: 0,
          total_messages: 0,
          total_cost: 0,
          total_invocations: 0
        }
      });
    }

    // Processar dados para gráficos
    const processedData = processDataForCharts(data, groupBy);



    return NextResponse.json(processedData);

  } catch (error) {
    console.error('❌ Erro na API de uso:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

function processDataForCharts(data: any[], groupBy: string) {
  console.log('🔄 Processando dados para gráficos:', {
    totalRecords: data.length,
    groupBy,
    firstRecord: data[0] ? {
      date: data[0].response_timestamp,
      invocation_id: data[0].invocation_id
    } : null
  });

  // Agrupar por invocation_id primeiro para contar mensagens únicas
  const invocationGroups = data.reduce((acc: Record<string, any>, item: any) => {
    const invocationId = item.invocation_id;

    if (!acc[invocationId]) {
      acc[invocationId] = {
        invocation_id: invocationId,
        date: item.response_timestamp, // Manter timestamp completo para processamento correto
        total_tokens: 0,
        total_cost: 0,
        message_count: 1, // Cada invocation_id representa uma conversa
        responses: []
      };
    }

    // Somar tokens de todas as respostas da mesma conversa
    acc[invocationId].total_tokens +=
      (item.prompt_token_count || 0) +
      (item.candidates_token_count || 0) +
      (item.thoughts_token_count || 0);

    acc[invocationId].total_cost += item.total_cost_usd || 0;
    acc[invocationId].responses.push(item);

    return acc;
  }, {});

  const invocations = Object.values(invocationGroups);

  console.log('📅 Invocações agrupadas:', {
    totalInvocations: invocations.length,
    firstInvocation: invocations[0] ? {
      date: invocations[0].date,
      dateExtracted: invocations[0].date.split('T')[0]
    } : null
  });

  // Agrupar por período (dia, semana, mês)
  const timeGroups: Record<string, any> = invocations.reduce((acc: Record<string, any>, invocation: any) => {
    // Extrair apenas a parte da data (YYYY-MM-DD) do timestamp
    // Isso evita conversões de timezone desnecessárias
    let dateString: string;
    try {
      // O timestamp vem no formato: "2025-05-31T22:16:04.35-03:00"
      // Extrair apenas a parte da data: "2025-05-31"
      dateString = invocation.date.split('T')[0];

      // Verificar se a data está no formato correto
      if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        console.error('Formato de data inválido:', invocation.date);
        return acc;
      }
    } catch (error) {
      console.error('Erro ao processar data:', invocation.date, error);
      return acc;
    }

    let key: string;

    switch (groupBy) {
      case 'week':
        // Para semana, usar a data como está e calcular o início da semana
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day); // Criar data local sem timezone
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const weekYear = weekStart.getFullYear();
        const weekMonth = String(weekStart.getMonth() + 1).padStart(2, '0');
        const weekDay = String(weekStart.getDate()).padStart(2, '0');
        key = `${weekYear}-${weekMonth}-${weekDay}`;
        break;
      case 'month':
        // Para mês, usar apenas ano-mês
        key = dateString.substring(0, 7); // "2025-05"
        break;
      default: // day
        // Para dia, usar a data como está
        key = dateString; // "2025-05-31"
    }

    if (!acc[key]) {
      acc[key] = {
        date: key,
        total_tokens: 0,
        total_messages: 0,
        total_cost: 0
      };
    }

    acc[key].total_tokens += invocation.total_tokens;
    acc[key].total_messages += invocation.message_count; // Usar message_count em vez de total_messages
    acc[key].total_cost += invocation.total_cost;

    return acc;
  }, {} as Record<string, any>);

  const chartData = (Object.values(timeGroups) as any[]).sort((a: any, b: any) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  console.log('📈 Dados finais do gráfico:', {
    chartDataLength: chartData.length,
    chartDates: chartData.map(item => item.date),
    firstChartItem: chartData[0]
  });

  // Calcular totais
  const totals = {
    total_tokens: invocations.reduce((sum: number, inv: any) => sum + inv.total_tokens, 0),
    total_messages: invocations.length, // Número de invocations = número de conversas
    total_cost: invocations.reduce((sum: number, inv: any) => sum + inv.total_cost, 0),
    total_invocations: invocations.length
  };



  return {
    chartData,
    totals
  };
}
