"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>tor,
  Bar<PERSON>hart3,
  DollarSign,
  Target
} from "lucide-react";
import { useIsMobile } from "@/components/hooks/use-mobile";

interface SuggestionCard {
  id: string;
  title: string;
  description: string;
  prompt: string;
  icon: React.ReactNode;
  category: "analysis" | "data" | "calculation" | "investment";
}

interface SuggestionCardsProps {
  onSuggestionClick: (prompt: string) => void;
}

const suggestions: SuggestionCard[] = [
  {
    id: "vale3-analysis",
    title: "Ações",
    description: "Fechamento dos últimos 10 pregões da PETR4",
    prompt: "Fechamento dos últimos 10 pregões da PETR4",
    icon: <TrendingUp className="h-4 w-4" />,
    category: "analysis"
  },
  {
    id: "portfolio-diversification",
    title: "Curvas B3",
    description: "Calcule qual a inflação implícita entre a LTN jul/2027 e a NTN-B com vencimento mais próximo?",
    prompt: "Qual a inflação implícita entre a LTN jul/2027 e a NTN-B com vencimento mais próximo?",
    icon: <Calculator className="h-4 w-4" />,
    category: "investment"
  },
  { 
    id: "fixed-income",
    title: "Fundos de investimento",
    description: "Quais fundos de Renda Fixa apresentaram aumento no patrimônio líquido em mais de R$ 100 milhões no último mês?",
    prompt: "Quais fundos de Renda Fixa apresentaram aumento no patrimônio líquido em mais de R$ 100 milhões no último mês?",
    icon: <PieChart className="h-4 w-4" />,
    category: "data"
  },
  {
    id: "market-trends",
    title: "Debêntures",
    description: "Com base no último fechamento da debênture VALE48, busque qual spread de crédito médio em relação à curva NTN-B de duration equivalente?",
    prompt: "Com base no último fechamento da debênture VALE48, qual spread de crédito médio em relação à curva NTN-B de duration equivalente?",
    icon: <BarChart3 className="h-4 w-4" />,
    category: "analysis"
  },
  {
    id: "investment-calculation",
    title: "CRI e CRA",
    description: "Quais foram os 10 maiores volumes financeiros de emissores de CRI negociadas no mercado secundário no último mês?",
    prompt: "Quais foram os 10 maiores volumes financeiros de emissores de CRI negociadas no mercado secundário no último mês?",
    icon: <DollarSign className="h-4 w-4" />,
    category: "calculation"
  },
  {
    id: "financial-goals",
    title: "Cruzamento de dados",
    description: "No último mês, qual relação entre o volume médio negociado e taxa indicativa média das debêntures indexadas ao CDI+?",
    prompt: "No último mês, qual relação entre o volume médio negociado e taxa indicativa média das debêntures indexadas ao CDI+?",
    icon: <Target className="h-4 w-4" />,
    category: "investment"
  }
];

const categoryColors = {
  analysis: "border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-700",
  data: "border-green-200 bg-green-50 hover:bg-green-100 text-green-700",
  calculation: "border-purple-200 bg-purple-50 hover:bg-purple-100 text-purple-700",
  investment: "border-orange-200 bg-orange-50 hover:bg-orange-100 text-orange-700"
};

export default function SuggestionCards({ onSuggestionClick }: SuggestionCardsProps) {
  const isMobile = useIsMobile();

  return (
    <div className={`w-full mx-auto ${isMobile ? 'max-w-sm px-2' : 'max-w-2xl'}`}>
      <div className={`text-center ${isMobile ? 'mb-4' : 'mb-6'}`}>
        <h3 className={`font-semibold mb-2 ${isMobile ? 'text-base' : 'text-lg'}`}>Como posso te ajudar hoje?</h3>
        <p className={`text-muted-foreground ${isMobile ? 'text-xs px-2' : 'text-sm'}`}>
          Clique em uma das sugestões abaixo para começar nossa conversa
        </p>
      </div>

      <div className={`grid gap-3 ${isMobile ? 'grid-cols-2' : 'grid-cols-3'}`}>
        {suggestions.map((suggestion) => (
          <div
            key={suggestion.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md border-2 rounded-lg flex flex-col items-center justify-center text-center ${
              isMobile ? 'p-2 h-20' : 'p-3 h-24'
            } ${categoryColors[suggestion.category]}`}
            onClick={() => onSuggestionClick(suggestion.prompt)}
          >
            <div className={`${isMobile ? 'mb-0.5' : 'mb-1'}`}>
              {suggestion.icon}
            </div>
            <h4 className={`font-medium leading-tight ${isMobile ? 'text-[10px] mb-0.5' : 'text-xs mb-1'}`}>
              {suggestion.title}
            </h4>
            <p className={`opacity-70 leading-tight ${isMobile ? 'text-[8px]' : 'text-[10px]'}`}>
              {suggestion.description}
            </p>
          </div>
        ))}
      </div>

      <div className={`text-center ${isMobile ? 'mt-4' : 'mt-6'}`}>
        <p className={`text-muted-foreground ${isMobile ? 'text-[10px] px-4' : 'text-xs'}`}>
          Ou digite sua própria pergunta na caixa de texto abaixo
        </p>
      </div>
    </div>
  );
}
