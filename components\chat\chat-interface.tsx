"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SendIcon, AlertTriangle, PlusCircle } from "lucide-react";
import ChatMessage from "./chat-message";
import { ChatMessage as ChatMessageType } from "./types";
import { v4 as uuidv4 } from "uuid";
import { useSession } from "./session-context";
import TypingIndicator from "./typing-indicator";
import EnhancedTypingIndicator from "./enhanced-typing-indicator";
import { useTypingIndicator } from "./hooks/use-typing-indicator";
import SuggestionCards from "./suggestion-cards";
import NewConversationScreen from "./new-conversation-screen";
import MobileSidebar from "./mobile-sidebar";
import { useIsMobile } from "@/components/hooks/use-mobile";
import { useCreditsContext } from "@/components/providers/credits-provider";
import { AppConfig } from "@/lib/config/app-config";
import { pLogger } from "@/lib/utils/production-logger";


interface ChatInterfaceProps {
  userId: string;
}

export default function ChatInterface({ userId }: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Hook personalizado para gerenciar typing indicator
  const {
    showTypingIndicator,
    startTyping,
    stopTyping,
    resetTyping,
    isTypingExpired
  } = useTypingIndicator();

  const {
    currentSessionId,
    createNewSession,
    getCurrentSession,
    addMessageToSession,
    isCreatingSession
  } = useSession();

  const { decrementCredits } = useCreditsContext();

  // Rolar para o final da conversa quando novas mensagens chegarem ou indicador de digitação aparecer
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [getCurrentSession()?.messages, showTypingIndicator]);

  const sendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputValue.trim();
    if (!textToSend || isLoading || !currentSessionId) return;

    setInputValue("");
    setIsLoading(true);
    startTyping();
    setErrorMessage(null);

    // Adicionar mensagem do usuário
    const userMessage: ChatMessageType = {
      id: uuidv4(),
      content: { text: textToSend },
      role: "user",
      timestamp: new Date().toISOString(),
    };

    addMessageToSession(currentSessionId, userMessage);

    try {
      // Usar resposta simples (SEM streaming)
      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          app_name: process.env.NEXT_PUBLIC_LOCAL_AGENT_NAME || "financial_agent",
          user_id: userId,
          session_id: currentSessionId,
          new_message: {
            parts: [{ text: textToSend }],
            role: "user"
          },
          streaming: false
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      pLogger.chat('Resposta recebida', { messageCount: Array.isArray(data) ? data.length : 1 });

      const processedMessages = processResponse(data);

      // Se recebemos mensagens válidas do agente, decrementar créditos
      const hasValidAgentResponse = processedMessages.some(msg =>
        msg.role === "assistant" &&
        (msg.content.text || msg.content.echarts) &&
        !msg.content.text.includes("Créditos insuficientes") &&
        !msg.content.text.includes("Erro")
      );

      if (hasValidAgentResponse) {
        pLogger.chat('Resposta válida recebida, decrementando créditos...');
        decrementCredits();
      }

      for (const msg of processedMessages) {
        addMessageToSession(currentSessionId, msg);
      }
    } catch (error: any) {
      pLogger.error("Erro ao enviar mensagem", error, { context: 'CHAT' });
      setErrorMessage("Erro ao processar sua solicitação");
    } finally {
      setIsLoading(false);
      stopTyping();
    }
  };

  const processResponse = (responseData: any[]): ChatMessageType[] => {
    const messages: ChatMessageType[] = [];

    for (const item of responseData) {
      if (item.content && item.content.parts) {
        let messageContent: any = { text: "", echarts: undefined };
        let hasText = false;

        for (const part of item.content.parts) {
          // Texto normal
          if (part.text) {
            const processedText = processTextForCharts(part.text);
            messageContent.text += processedText.text;

            if (processedText.hasCharts && processedText.chartType === 'echarts') {
              messageContent.echarts = processedText.chartConfig;

              if (!processedText.text || processedText.text.trim() === '') {
                messageContent.text = '';
              }
            }
            hasText = true;
          }

          // Gráfico ECharts via function call
          if (part.functionCall && part.functionCall.name === "echarts_tool") {
            try {
              const echartsData = typeof part.functionCall.args.chart_config === 'string'
                ? JSON.parse(part.functionCall.args.chart_config)
                : part.functionCall.args.chart_config;
              messageContent.echarts = echartsData;
            } catch (error) {
              console.error("Erro ao processar dados ECharts:", error);
              messageContent.text += `\n[Erro ao renderizar gráfico ECharts]`;
              hasText = true;
            }
          }
        }

        if (messageContent.echarts || hasText) {
          if (messageContent.echarts && (!messageContent.text || messageContent.text.trim() === '')) {
            messageContent.text = '';
          }

          messages.push({
            id: item.id || uuidv4(),
            content: messageContent,
            role: item.content.role === "model" ? "assistant" : "user",
            timestamp: new Date(item.timestamp || Date.now()).toISOString(),
          });
        }
      }
    }

    return messages;
  };

  // Função para converter formato Python para JSON válido
  const convertPythonToJSON = (pythonStr: string): string => {
    let jsonStr = pythonStr;

    // Converter aspas simples para duplas
    jsonStr = jsonStr.replace(/'/g, '"');

    // Converter True/False/None para true/false/null
    jsonStr = jsonStr.replace(/\bTrue\b/g, 'true');
    jsonStr = jsonStr.replace(/\bFalse\b/g, 'false');
    jsonStr = jsonStr.replace(/\bNone\b/g, 'null');

    return jsonStr;
  };

  const processTextForCharts = (text: string) => {
    const chartPatterns = [
      /```json\n([\s\S]*?)\n```/, // JSON em blocos de código
      /(\{[\s\S]*?\})/g, // JSON direto no texto
    ];

    let cleanText = text;
    let hasCharts = false;
    let chartConfig = null;
    let chartType = 'echarts'; // Sempre ECharts

    pLogger.charts('Processando texto para gráficos', {
      textLength: text.length,
      hasJsonBlock: text.includes('```json')
      // Preview removido por segurança
    });

    for (let i = 0; i < chartPatterns.length; i++) {
      const pattern = chartPatterns[i];

      if (i === 0) {
        // Primeiro padrão: JSON em blocos de código
        const match = text.match(pattern);
        if (match) {
          try {
            let jsonStr = match[1].trim();
            jsonStr = convertPythonToJSON(jsonStr);
            const parsedConfig = JSON.parse(jsonStr);

            if (parsedConfig && (parsedConfig.series || parsedConfig.xAxis || parsedConfig.yAxis || parsedConfig.option)) {
              hasCharts = true;
              chartConfig = parsedConfig.option || parsedConfig;
              chartType = 'echarts';
              cleanText = text.replace(match[0], '').trim();
              pLogger.charts('Gráfico detectado em bloco de código');
              break;
            }
          } catch (error) {
            console.warn('Erro ao parsear JSON em bloco de código:', error);
          }
        }
      } else {
        // Segundo padrão: JSON direto no texto
        const matches = Array.from(text.matchAll(pattern));

        for (const match of matches) {
          try {
            let jsonStr = match[1].trim();
            jsonStr = convertPythonToJSON(jsonStr);
            const parsedConfig = JSON.parse(jsonStr);

            // Verificar se é uma configuração ECharts válida
            if (parsedConfig &&
                typeof parsedConfig === 'object' &&
                (parsedConfig.series || parsedConfig.xAxis || parsedConfig.yAxis || parsedConfig.title)) {

              // Verificar se tem estrutura mínima de gráfico
              const hasValidStructure =
                (parsedConfig.series && Array.isArray(parsedConfig.series)) ||
                (parsedConfig.xAxis && parsedConfig.yAxis) ||
                (parsedConfig.title && (parsedConfig.series || parsedConfig.data));

              if (hasValidStructure) {
                hasCharts = true;
                chartConfig = parsedConfig.option || parsedConfig;
                chartType = 'echarts';
                cleanText = text.replace(match[0], '').trim();
                pLogger.charts('Gráfico detectado no texto direto', {
                  hasTitle: !!parsedConfig.title,
                  hasSeries: !!parsedConfig.series,
                  hasXAxis: !!parsedConfig.xAxis,
                  hasYAxis: !!parsedConfig.yAxis
                });
                break;
              }
            }
          } catch (error) {
            // Silenciar erros de JSON inválido para não poluir o console
            // console.warn('JSON inválido encontrado:', error);
          }
        }

        if (hasCharts) break;
      }
    }

    pLogger.charts('Resultado do processamento', {
      hasCharts,
      chartType,
      chartConfigExists: !!chartConfig,
      cleanTextLength: cleanText.length
    });

    return {
      text: cleanText,
      hasCharts,
      chartConfig,
      chartType
    };
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleSendClick = () => {
    sendMessage();
  };

  const handleSuggestionClick = (prompt: string) => {
    sendMessage(prompt);
  };

  const handleNewChat = async () => {
    try {
      await createNewSession(userId);
    } catch (error) {
      pLogger.error("Erro ao criar nova sessão", error, { context: 'CHAT' });
      setErrorMessage("Não foi possível iniciar uma nova conversa. Tente novamente.");
    }
  };

  const currentSession = getCurrentSession();
  const messages = currentSession?.messages || [];

  // Se não há sessão ativa, mostrar tela de nova conversa
  if (!currentSessionId) {
    return (
      <div className="flex flex-col h-screen w-full bg-background">
        {/* Cabeçalho simplificado */}
        <div className="border-b border-border p-3 flex items-center justify-between flex-shrink-0 h-14">
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {isMobile && AppConfig.enableSessionHistory && <MobileSidebar userId={userId} />}
            <h2 className={`font-medium ${isMobile ? 'text-base' : 'text-lg'}`}>PrimeAI</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleNewChat}
              disabled={isCreatingSession}
              className={`flex items-center gap-2 ${isMobile ? 'text-xs px-2' : ''}`}
            >
              <PlusCircle className="h-4 w-4" />
              {isMobile ? "Nova conversa" : "Nova Conversa"}
            </Button>
          </div>
        </div>

        {/* Tela de nova conversa */}
        <NewConversationScreen
          onCreateNewChat={handleNewChat}
          isCreatingSession={isCreatingSession}
        />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen w-full bg-background">
      {/* Cabeçalho da conversa */}
      <div className="border-b border-border p-3 flex items-center justify-between flex-shrink-0 h-14">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {isMobile && AppConfig.enableSessionHistory && <MobileSidebar userId={userId} />}
          <div className="flex flex-col flex-1 min-w-0">
            {/* <div className="flex items-center gap-2">
              <h2 className={`font-medium truncate ${isMobile ? 'text-sm max-w-[180px]' : 'text-lg'}`}>
                {currentSession?.name || "Selecione ou crie uma conversa"}
              </h2>
            </div> */}
            {currentSession?.sessionCode && (
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                <span className={isMobile ? 'text-[10px]' : ''}>ID:</span>
                <code className={`bg-muted px-1 py-0.5 rounded font-mono truncate ${isMobile ? 'text-[9px] max-w-[100px]' : 'text-xs max-w-[120px]'}`}>
                  {currentSession.sessionCode}
                </code>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleNewChat}
            disabled={isCreatingSession}
            className={`flex items-center gap-2 ${isMobile ? 'text-xs px-2' : ''}`}
          >
            <PlusCircle className="h-4 w-4" />
            {isMobile ? "Nova Conversa" : "Nova Conversa"}
          </Button>
        </div>
      </div>

      {/* Área de mensagens */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {errorMessage && (
          <div className="bg-destructive/10 border border-destructive/20 text-destructive px-4 py-3 rounded-lg flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 flex-shrink-0" />
            <span className="text-sm">{errorMessage}</span>
          </div>
        )}

        {messages.length === 0 && !showTypingIndicator && currentSessionId && (
          <SuggestionCards onSuggestionClick={handleSuggestionClick} />
        )}

        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}

        {showTypingIndicator && (
          AppConfig.typingIndicator.enableCountdown ? (
            <EnhancedTypingIndicator
              onComplete={() => {
                // Callback opcional quando o tempo expira
                pLogger.debug("Tempo de resposta esperado expirou", undefined, { context: 'CHAT' });
              }}
            />
          ) : (
            <TypingIndicator />
          )
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Área de input */}
      <div className="border-t border-border p-4 flex-shrink-0">
        <div className="flex gap-2">
          <textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={currentSessionId ? "Digite sua mensagem..." : "Crie uma nova conversa para começar"}
            className="flex-1 min-h-[44px] max-h-32 px-3 py-2 border border-input rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-ring"
            disabled={isLoading || !currentSessionId}
          />
          <Button
            onClick={handleSendClick}
            disabled={isLoading || !inputValue.trim() || !currentSessionId}
            size="icon"
            className="h-11 w-11 flex-shrink-0"
          >
            <SendIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
