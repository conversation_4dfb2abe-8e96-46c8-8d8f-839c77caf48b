"use client";

import EChartsRenderer from "./echarts-renderer";

// Exemplos de gráficos para testar as melhorias
export const chartExamples = {
  // Gráfico com título e legenda que pode sobrepor
  lineChart: {
    title: {
      text: 'Evolução dos Preços de Ações',
      subtext: 'Dados históricos do último trimestre'
    },
    legend: {
      data: ['PETR4', 'VALE3', 'ITUB4', 'BBDC4', 'ABEV3']
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun']
    },
    yAxis: {
      type: 'value',
      name: 'Preç<PERSON> (R$)'
    },
    series: [
      {
        name: 'PETR4',
        type: 'line',
        data: [28.5, 29.2, 27.8, 30.1, 31.5, 29.8]
      },
      {
        name: 'VALE3',
        type: 'line',
        data: [65.2, 68.1, 62.5, 70.3, 72.8, 69.5]
      },
      {
        name: 'ITUB4',
        type: 'line',
        data: [25.8, 26.5, 24.9, 27.2, 28.1, 26.7]
      },
      {
        name: 'BBDC4',
        type: 'line',
        data: [22.3, 23.1, 21.8, 24.5, 25.2, 23.9]
      },
      {
        name: 'ABEV3',
        type: 'line',
        data: [14.2, 14.8, 13.9, 15.1, 15.6, 14.5]
      }
    ]
  },

  // Gráfico de barras com valores desproporcionais
  barChart: {
    title: {
      text: 'Volume de Negociação por Setor'
    },
    legend: {
      data: ['Q1', 'Q2', 'Q3', 'Q4']
    },
    xAxis: {
      type: 'category',
      data: ['Financeiro', 'Petróleo', 'Mineração', 'Tecnologia', 'Varejo']
    },
    yAxis: {
      type: 'value',
      name: 'Volume (Milhões R$)'
    },
    series: [
      {
        name: 'Q1',
        type: 'bar',
        data: [1200, 800, 1500, 300, 600]
      },
      {
        name: 'Q2',
        type: 'bar',
        data: [1100, 900, 1400, 350, 650]
      },
      {
        name: 'Q3',
        type: 'bar',
        data: [1300, 750, 1600, 400, 700]
      },
      {
        name: 'Q4',
        type: 'bar',
        data: [1400, 850, 1700, 450, 750]
      }
    ]
  },

  // Gráfico de pizza com muitos elementos
  pieChart: {
    title: {
      text: 'Distribuição de Investimentos por Classe de Ativo',
      left: 'center'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: ['Ações', 'Renda Fixa', 'Fundos Imobiliários', 'Criptomoedas', 'Commodities', 'Câmbio']
    },
    series: [
      {
        name: 'Investimentos',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35, name: 'Ações' },
          { value: 30, name: 'Renda Fixa' },
          { value: 15, name: 'Fundos Imobiliários' },
          { value: 10, name: 'Criptomoedas' },
          { value: 7, name: 'Commodities' },
          { value: 3, name: 'Câmbio' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
};

// Componente para testar os gráficos
export default function ChartTestExamples() {
  return (
    <div className="space-y-8 p-4">
      <h2 className="text-2xl font-bold">Teste de Gráficos ECharts</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">Gráfico de Linhas</h3>
          <EChartsRenderer option={chartExamples.lineChart} height={400} />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Gráfico de Barras</h3>
          <EChartsRenderer option={chartExamples.barChart} height={400} />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Gráfico de Pizza</h3>
          <EChartsRenderer option={chartExamples.pieChart} height={400} />
        </div>
      </div>
    </div>
  );
}
