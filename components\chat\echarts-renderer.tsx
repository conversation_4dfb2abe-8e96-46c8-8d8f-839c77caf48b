"use client";

import { useEffect, useLayoutEffect, useRef, useState } from "react";
import * as echarts from "echarts";
import { useIsMobile } from "@/components/hooks/use-mobile";

interface EChartsRendererProps {
  option: any;
  height?: number;
  className?: string;
}

// Função para calcular range automático do eixo Y
function calculateYAxisRange(series: any[], chartType?: string) {
  if (!series || series.length === 0) return {};

  let allValues: number[] = [];

  // Extrair todos os valores numéricos das séries
  series.forEach(serie => {
    if (!serie.data) return;

    serie.data.forEach((item: any) => {
      if (serie.type === 'candlestick') {
        // Para candlestick, considerar todos os valores OHLC
        if (Array.isArray(item)) {
          // Formato: [open, close, low, high] ou [timestamp, open, close, low, high]
          const values = item.length === 4 ? item : item.slice(1);
          allValues.push(...values.filter(v => typeof v === 'number'));
        } else if (typeof item === 'object' && item !== null) {
          // Formato objeto com propriedades OHLC
          const ohlc = [item.open, item.high, item.low, item.close].filter(v => typeof v === 'number');
          allValues.push(...ohlc);
        }
      } else if (typeof item === 'number') {
        // Valores simples
        allValues.push(item);
      } else if (Array.isArray(item)) {
        // Arrays de valores [x, y] ou [timestamp, value]
        const numericValues = item.filter(v => typeof v === 'number');
        if (numericValues.length > 0) {
          allValues.push(numericValues[numericValues.length - 1]); // Usar último valor (geralmente Y)
        }
      } else if (typeof item === 'object' && item !== null) {
        // Objetos com propriedade value
        if (typeof item.value === 'number') {
          allValues.push(item.value);
        } else if (Array.isArray(item.value)) {
          const numericValues = item.value.filter((v: any) => typeof v === 'number');
          if (numericValues.length > 0) {
            allValues.push(numericValues[numericValues.length - 1]);
          }
        }
      }
    });
  });

  if (allValues.length === 0) return {};

  // Remover outliers extremos (opcional - pode ser desabilitado se necessário)
  const sortedValues = [...allValues].sort((a, b) => a - b);
  const q1Index = Math.floor(sortedValues.length * 0.25);
  const q3Index = Math.floor(sortedValues.length * 0.75);
  const q1 = sortedValues[q1Index];
  const q3 = sortedValues[q3Index];
  const iqr = q3 - q1;

  // Usar valores filtrados apenas se houver outliers significativos
  const filteredValues = iqr > 0 ? allValues.filter(v =>
    v >= (q1 - 1.5 * iqr) && v <= (q3 + 1.5 * iqr)
  ) : allValues;

  // Se filtrar removeu muitos valores, usar todos
  const valuesToUse = filteredValues.length < allValues.length * 0.8 ? allValues : filteredValues;

  const min = Math.min(...valuesToUse);
  const max = Math.max(...valuesToUse);
  const range = max - min;

  // Calcular padding baseado no tipo de gráfico e range
  let paddingPercent = 0.1; // 10% padrão

  if (chartType === 'candlestick') {
    paddingPercent = 0.05; // 5% para candlestick (mais conservador)
  } else if (range === 0) {
    paddingPercent = 0.1; // Se todos valores são iguais, usar 10%
  } else if (range < 1) {
    paddingPercent = 0.2; // 20% para valores pequenos
  }

  const padding = Math.max(range * paddingPercent, Math.abs(max) * 0.01); // Mínimo 1% do valor máximo

  const calculatedMin = min - padding;
  const calculatedMax = max + padding;

  // Para valores financeiros, evitar valores negativos se todos os dados são positivos
  const finalMin = min >= 0 && calculatedMin < 0 ? 0 : calculatedMin;

  console.log('ECharts Y-Axis Auto Range:', {
    originalValues: allValues.length,
    filteredValues: valuesToUse.length,
    min: finalMin,
    max: calculatedMax,
    range,
    chartType
  });

  return {
    min: finalMin,
    max: calculatedMax,
    // Configurar escala apropriada
    scale: true,
    // Melhorar formatação dos labels
    axisLabel: {
      formatter: (value: number) => {
        if (Math.abs(value) >= 1000000) {
          return (value / 1000000).toFixed(1) + 'M';
        } else if (Math.abs(value) >= 1000) {
          return (value / 1000).toFixed(1) + 'K';
        } else if (Math.abs(value) < 1 && Math.abs(value) > 0) {
          return value.toFixed(4);
        } else {
          return value.toFixed(2);
        }
      }
    }
  };
}

// Função para validar e corrigir configuração ECharts
function validateAndFixEChartsConfig(option: any): any {
  if (!option || typeof option !== 'object') {
    console.error('ECharts: Configuração inválida ou vazia');
    return null;
  }

  // Validar se há pelo menos uma série
  if (!option.series || (Array.isArray(option.series) && option.series.length === 0)) {
    console.error('ECharts: Nenhuma série encontrada');
    return null;
  }

  const series = Array.isArray(option.series) ? option.series : [option.series];

  // Validar cada série
  for (let i = 0; i < series.length; i++) {
    const serie = series[i];
    if (!serie || typeof serie !== 'object') {
      console.error(`ECharts: Série ${i} inválida`);
      return null;
    }

    // Garantir que cada série tenha um tipo válido
    if (!serie.type) {
      serie.type = 'line'; // Tipo padrão
      console.warn(`ECharts: Série ${i} sem tipo, usando 'line' como padrão`);
    }

    // Validar dados da série
    if (!serie.data || !Array.isArray(serie.data)) {
      console.error(`ECharts: Série ${i} sem dados válidos`);
      return null;
    }

    // Para séries que precisam de coordinateSystem, garantir que seja definido
    if (['line', 'bar', 'scatter', 'candlestick'].includes(serie.type)) {
      if (!serie.coordinateSystem) {
        serie.coordinateSystem = 'cartesian2d'; // Sistema de coordenadas padrão
      }
    }
  }

  // Corrigir configuração de grid se estiver malformada
  if (option.grid) {
    option.grid = fixGridConfiguration(option.grid);
  }

  return option;
}

// Função para corrigir configuração de grid malformada
function fixGridConfiguration(grid: any): any {
  if (!grid) return {};

  // Se o grid tem propriedades numéricas como "0", "1", etc., é um grid multi-chart
  const hasNumericKeys = Object.keys(grid).some(key => !isNaN(Number(key)));

  if (hasNumericKeys) {
    // Extrair apenas as configurações de grid válidas (com chaves numéricas)
    const gridArray: any[] = [];
    Object.keys(grid).forEach(key => {
      if (!isNaN(Number(key))) {
        gridArray[Number(key)] = grid[key];
      }
    });

    // Se há apenas um grid, usar como objeto único
    if (gridArray.length === 1) {
      return gridArray[0];
    }

    // Se há múltiplos grids, retornar como array
    if (gridArray.length > 1) {
      return gridArray;
    }
  }

  // Se não há chaves numéricas, usar o grid como está (removendo propriedades numéricas se existirem)
  const cleanGrid = { ...grid };
  Object.keys(cleanGrid).forEach(key => {
    if (!isNaN(Number(key))) {
      delete cleanGrid[key];
    }
  });

  return cleanGrid;
}

// Função para otimizar as opções do gráfico
function optimizeChartOptions(option: any, isMobile: boolean) {
  // Primeiro, validar e corrigir a configuração
  const validatedOption = validateAndFixEChartsConfig(option);
  if (!validatedOption) {
    console.error('ECharts: Configuração inválida, não é possível renderizar');
    return null;
  }

  // Normalizar configuração: garantir que campos obrigatórios existam
  const normalizedOption = {
    ...validatedOption,
    // Normalizar título se for string
    title: typeof validatedOption.title === 'string'
      ? { text: validatedOption.title }
      : validatedOption.title,
    // Garantir que series seja array
    series: Array.isArray(validatedOption.series)
      ? validatedOption.series
      : (validatedOption.series ? [validatedOption.series] : []),
    // Garantir estruturas básicas de eixos com tipos apropriados
    xAxis: (() => {
      const xAxis = validatedOption.xAxis || {};
      if (Array.isArray(xAxis)) {
        return xAxis.map(axis => ({ type: 'category', ...axis }));
      }
      return { type: 'category', ...xAxis };
    })(),
    yAxis: (() => {
      const yAxis = validatedOption.yAxis || {};
      if (Array.isArray(yAxis)) {
        return yAxis.map(axis => ({ type: 'value', ...axis }));
      }
      return { type: 'value', ...yAxis };
    })()
  };

  // Calcular espaçamentos dinâmicos baseados no conteúdo
  // Verificar se há título (agora sempre será objeto ou null)
  const hasTitle = normalizedOption.title && normalizedOption.title.text;
  const hasLegend = normalizedOption.legend && normalizedOption.legend.data && normalizedOption.legend.data.length > 0;
  const hasToolbox = normalizedOption.toolbox && normalizedOption.toolbox.feature;

  // Espaçamentos base - otimizados para aproveitar melhor o espaço
  let topSpace = isMobile ? 35 : 30;
  let bottomSpace = isMobile ? 25 : 50; // Reduzido drasticamente no mobile
  let leftSpace = isMobile ? 50 : 60;
  let rightSpace = isMobile ? 35 : 30;

  // Ajustar espaços baseado nos elementos presentes
  if (hasTitle) {
    topSpace += isMobile ? 30 : 40;
  }

  if (hasLegend) {
    const legendOrient = option.legend.orient || 'horizontal';
    if (legendOrient === 'horizontal') {
      topSpace += isMobile ? 25 : 35;
    } else {
      rightSpace += isMobile ? 80 : 100;
    }
  }

  if (hasToolbox) {
    topSpace += isMobile ? 20 : 25;
  }

  // Corrigir cores do candlestick se presente
  const correctedOption = { ...normalizedOption };
  if (correctedOption.series) {
    correctedOption.series = Array.isArray(correctedOption.series)
      ? correctedOption.series.map((serie: any) => {
          if (serie.type === 'candlestick') {
            return {
              ...serie,
              itemStyle: {
                // Forçar cores corretas, ignorando as do agente
                color: '#22c55e',      // Verde para alta (ECharts: color = fechamento > abertura)
                color0: '#ef4444',     // Vermelho para baixa (ECharts: color0 = fechamento < abertura)
                borderColor: '#16a34a', // Borda verde para alta
                borderColor0: '#dc2626', // Borda vermelha para baixa
                // Não usar ...serie.itemStyle para não sobrescrever as cores corretas
              }
            };
          }
          return serie;
        })
      : correctedOption.series.type === 'candlestick'
        ? {
            ...correctedOption.series,
            itemStyle: {
              // Forçar cores corretas, ignorando as do agente
              color: '#22c55e',      // Verde para alta (ECharts: color = fechamento > abertura)
              color0: '#ef4444',     // Vermelho para baixa (ECharts: color0 = fechamento < abertura)
              borderColor: '#16a34a', // Borda verde para alta
              borderColor0: '#dc2626', // Borda vermelha para baixa
              // Não usar ...correctedOption.series.itemStyle para não sobrescrever as cores corretas
            }
          }
        : correctedOption.series;
  }

  // Calcular range automático do eixo Y se não estiver definido
  const series = Array.isArray(correctedOption.series) ? correctedOption.series : [correctedOption.series];
  const primaryChartType = series[0]?.type || 'line';
  const autoYAxisRange = calculateYAxisRange(series, primaryChartType);

  // Aplicar range automático ao eixo Y se não houver configuração manual
  if (autoYAxisRange.min !== undefined && autoYAxisRange.max !== undefined) {
    if (Array.isArray(correctedOption.yAxis)) {
      correctedOption.yAxis = correctedOption.yAxis.map((axis: any) => ({
        ...axis,
        // Só aplicar se não houver min/max definidos manualmente
        ...(axis.min === undefined && axis.max === undefined ? autoYAxisRange : {})
      }));
    } else {
      const currentYAxis = correctedOption.yAxis || {};
      if (currentYAxis.min === undefined && currentYAxis.max === undefined) {
        correctedOption.yAxis = {
          ...currentYAxis,
          ...autoYAxisRange
        };
      }
    }
  }

  return {
    ...correctedOption,
    // Configurações de título otimizadas
    title: hasTitle ? {
      ...correctedOption.title,
      top: isMobile ? 10 : 15,
      left: 'center',
      textStyle: {
        fontSize: isMobile ? 10 : 16, // Título menor no mobile
        fontWeight: 'bold',
        overflow: 'break',
        width: isMobile ? '90%' : 'auto',
        ...correctedOption.title.textStyle
      },
      subtextStyle: {
        fontSize: isMobile ? 8 : 12,
        ...correctedOption.title.subtextStyle
      }
    } : correctedOption.title,

    // Configurações de legenda otimizadas
    legend: hasLegend ? {
      ...correctedOption.legend,
      top: hasTitle ? (isMobile ? 45 : 55) : (isMobile ? 15 : 20),
      left: 'center',
      orient: isMobile ? 'horizontal' : (correctedOption.legend.orient || 'horizontal'),
      textStyle: {
        fontSize: isMobile ? 10 : 12,
        ...correctedOption.legend.textStyle
      },
      itemWidth: isMobile ? 15 : 20,
      itemHeight: isMobile ? 10 : 14,
      itemGap: isMobile ? 8 : 10
    } : correctedOption.legend,

    // Grid otimizado para evitar sobreposições
    grid: (() => {
      // Se já há uma configuração de grid válida (array ou objeto), preservá-la
      if (correctedOption.grid) {
        // Se é um array de grids (multi-chart), não modificar
        if (Array.isArray(correctedOption.grid)) {
          return correctedOption.grid;
        }

        // Se é um objeto, aplicar apenas otimizações que não quebrem a estrutura
        return {
          left: leftSpace,
          right: rightSpace,
          top: topSpace,
          bottom: bottomSpace,
          containLabel: true,
          // Para mobile, usar dimensões otimizadas para aproveitar o espaço
          ...(isMobile ? {
            width: Math.max(200, 320 - leftSpace - rightSpace),
            height: Math.max(160, 280 - topSpace - bottomSpace),
          } : {
            width: 'auto',
            height: 'auto'
          }),
          ...correctedOption.grid
        };
      }

      // Se não há grid, criar um padrão
      return {
        left: leftSpace,
        right: rightSpace,
        top: topSpace,
        bottom: bottomSpace,
        containLabel: true,
        ...(isMobile ? {
          width: Math.max(200, 320 - leftSpace - rightSpace),
          height: Math.max(160, 280 - topSpace - bottomSpace),
        } : {
          width: 'auto',
          height: 'auto'
        })
      };
    })(),

    // Toolbox otimizado
    toolbox: hasToolbox ? {
      ...correctedOption.toolbox,
      top: isMobile ? 5 : 10,
      right: isMobile ? 10 : 15,
      iconStyle: {
        borderWidth: 1,
        ...correctedOption.toolbox.iconStyle
      },
      feature: {
        ...correctedOption.toolbox.feature,
        saveAsImage: {
          pixelRatio: 2,
          ...correctedOption.toolbox.feature?.saveAsImage
        }
      }
    } : correctedOption.toolbox
  };
}

export default function EChartsRenderer({
  option,
  height = 400,
  className = ""
}: EChartsRendererProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const isMobile = useIsMobile();

  // Inicializar o gráfico
  useLayoutEffect(() => {
    if (!chartRef.current) return;

    // Limpar instância anterior se existir
    if (chartInstance.current) {
      chartInstance.current.dispose();
      chartInstance.current = null;
    }

    // Criar nova instância com configurações específicas para mobile
    const initOptions = isMobile ? {
      width: Math.min(350, chartRef.current.clientWidth),
      height: Math.min(320, chartRef.current.clientHeight),
      renderer: 'canvas' as const
    } : undefined;

    chartInstance.current = echarts.init(chartRef.current, null, initOptions);

    // Forçar o canvas a respeitar os limites no mobile
    if (isMobile && chartRef.current) {
      const canvas = chartRef.current.querySelector('canvas');
      if (canvas) {
        canvas.style.maxWidth = '100%';
        canvas.style.maxHeight = '100%';
        canvas.style.objectFit = 'contain';
      }
    }

    setIsInitialized(true);

    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
      setIsInitialized(false);
    };
  }, []);

  // Atualizar opções do gráfico
  useEffect(() => {
    if (!chartInstance.current || !option || !isInitialized) return;

    // Otimizar opções do gráfico
    let optimizedOption = optimizeChartOptions(option, isMobile);

    // Se a validação falhou, não tentar renderizar
    if (!optimizedOption) {
      console.error('ECharts: Não foi possível otimizar a configuração, abortando renderização');
      return;
    }

    // Configurações adicionais de eixos para melhor proporção
    const configureAxis = (axis: any) => {
      const baseConfig = {
        ...axis,
        axisLabel: {
          fontSize: isMobile ? 10 : 12,
          rotate: isMobile && axis.type !== 'value' ? 45 : 0,
          interval: 'auto',
          // Manter formatação personalizada se já existir
          ...(axis.axisLabel || {})
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#d1d5db',
            ...axis.axisLine?.lineStyle
          },
          ...axis.axisLine
        },
        axisTick: {
          show: true,
          ...axis.axisTick
        },
        splitLine: {
          show: axis.type === 'value',
          lineStyle: {
            color: '#f3f4f6',
            type: 'dashed',
            ...axis.splitLine?.lineStyle
          },
          ...axis.splitLine
        }
      };

      // Para eixos Y com range automático, garantir que a escala seja adequada
      if (axis.type === 'value' && axis.min !== undefined && axis.max !== undefined) {
        baseConfig.scale = true;
        baseConfig.splitNumber = isMobile ? 4 : 6; // Menos divisões no mobile
      }

      return baseConfig;
    };

    // Aplicar configurações de eixos
    if (optimizedOption.xAxis) {
      optimizedOption.xAxis = Array.isArray(optimizedOption.xAxis)
        ? optimizedOption.xAxis.map(configureAxis)
        : configureAxis(optimizedOption.xAxis);
    }

    if (optimizedOption.yAxis) {
      optimizedOption.yAxis = Array.isArray(optimizedOption.yAxis)
        ? optimizedOption.yAxis.map(configureAxis)
        : configureAxis(optimizedOption.yAxis);
    }

    // Configurações globais para melhor aparência
    optimizedOption = {
      ...optimizedOption,
      backgroundColor: 'transparent',
      animation: true,
      animationDuration: 750,
      animationEasing: 'cubicOut',
      // Forçar o gráfico a respeitar os limites do container
      ...(isMobile && {
        media: [{
          query: { maxWidth: 500 },
          option: {
            grid: {
              ...optimizedOption.grid,
              left: '15%',    // Menos restritivo
              right: '10%',   // Menos restritivo
              top: '20%',     // Menos restritivo
              bottom: '8%'    // Reduzido para aproveitar melhor o espaço
            }
          }
        }]
      })
    };

    // Log da configuração final para debug
    console.log('ECharts: Configuração final:', {
      hasSeries: !!optimizedOption.series,
      seriesCount: Array.isArray(optimizedOption.series) ? optimizedOption.series.length : 1,
      hasXAxis: !!optimizedOption.xAxis,
      hasYAxis: !!optimizedOption.yAxis,
      xAxisType: Array.isArray(optimizedOption.xAxis) ? optimizedOption.xAxis[0]?.type : optimizedOption.xAxis?.type,
      yAxisType: Array.isArray(optimizedOption.yAxis) ? optimizedOption.yAxis[0]?.type : optimizedOption.yAxis?.type
    });

    // Usar setTimeout para evitar conflito com main process
    const timeoutId = setTimeout(() => {
      if (chartInstance.current && isInitialized) {
        try {
          // Validação final antes de setOption
          if (!optimizedOption.series ||
              (Array.isArray(optimizedOption.series) && optimizedOption.series.length === 0)) {
            throw new Error('Configuração inválida: nenhuma série encontrada');
          }

          chartInstance.current.setOption(optimizedOption, true);

          // Aplicar restrições ao canvas após setOption no mobile
          if (isMobile && chartRef.current) {
            setTimeout(() => {
              const canvas = chartRef.current?.querySelector('canvas');
              if (canvas) {
                canvas.style.maxWidth = '100%';
                canvas.style.maxHeight = '100%';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.style.objectFit = 'contain';
                canvas.style.display = 'block';
              }
            }, 100);
          }

          console.log('ECharts configurado com sucesso');
        } catch (error) {
          console.error('Erro ao configurar ECharts:', error);
          console.error('Configuração que causou o erro:', JSON.stringify(optimizedOption, null, 2));
        }
      }
    }, 0);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [option, isMobile, isInitialized]);

  // Redimensionamento
  useEffect(() => {
    if (!chartInstance.current || !isInitialized) return;

    let resizeTimeout: NodeJS.Timeout;
    const handleResize = () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    // Trigger resize after initialization to ensure proper sizing
    setTimeout(handleResize, 100);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimeout);
    };
  }, [isInitialized]);

  return (
    <div
      className={`w-full relative overflow-hidden ${className}`}
      style={{
        maxWidth: '100%',
        ...(isMobile && {
          maxWidth: 'calc(100vw - 60px)', // Menos restritivo
          boxSizing: 'border-box'
        })
      }}
    >
      {!isInitialized && (
        <div
          className="flex items-center justify-center bg-gray-50 rounded"
          style={{
            height: isMobile ? Math.min(height, 320) : height,
            minHeight: isMobile ? 290 : 300,
          }}
        >
          <div className="text-sm text-gray-500">Carregando gráfico...</div>
        </div>
      )}
      {/* Container forçado para mobile */}
      <div
        className={`w-full overflow-hidden echarts-container ${isMobile ? 'relative' : ''}`}
        style={{
          ...(isMobile && {
            width: '100%',
            maxWidth: '100%',
            height: Math.min(height, 320),
            position: 'relative',
            overflow: 'hidden',
            border: '1px solid transparent' // Força o container
          })
        }}
      >
        <div
          ref={chartRef}
          className={`echarts-container ${!isInitialized ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          style={{
            width: '100%',
            height: isMobile ? Math.min(height, 320) : height,
            minHeight: isMobile ? 290 : 300,
            maxWidth: '100%',
            overflow: 'hidden',
            position: 'relative'
          }}
        />
      </div>
    </div>
  );
}
