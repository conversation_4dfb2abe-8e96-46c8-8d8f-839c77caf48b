    "use client";

import { forwardRef } from "react";
import { ChatMessage as ChatMessageType } from "./types";
import { Use<PERSON>, <PERSON><PERSON> } from "lucide-react";
import { useIsMobile } from "@/components/hooks/use-mobile";
import EChartsRenderer from "./echarts-renderer";
import TableRenderer from "./table-renderer";
import { marked } from "marked";

interface ChatMessageProps {
  message: ChatMessageType;
}

// Configurar marked para melhor renderização
marked.setOptions({
  breaks: true, // Quebras de linha simples viram <br>
  gfm: true,    // GitHub Flavored Markdown
});

const ChatMessage = forwardRef<HTMLDivElement, ChatMessageProps>(
  ({ message }, ref) => {
    const isUser = message.role === "user";
    const isMobile = useIsMobile();

    // Função para detectar se o texto contém uma tabela
    const hasTable = (text: string): boolean => {
      // Verificar se tem pelo menos 2 linhas com pipes
      const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      let tableLines = 0;
      let hasSeparator = false;

      for (const line of lines) {
        // Linha com pipes (pelo menos 1 pipe para formar 2 colunas)
        if (line.includes('|') && (line.match(/\|/g) || []).length >= 1) {
          tableLines++;
        }
        // Linha separadora (traços e pipes) - mais flexível
        else if (/^[\-\|\s]+$/.test(line) || /^\|?[\-\s]+\|[\-\|\s]*$/.test(line)) {
          hasSeparator = true;
        }
        // Linha separadora alternativa (só traços)
        else if (/^[\-]{3,}$/.test(line)) {
          hasSeparator = true;
        }
      }

      // Ser mais flexível - aceitar tabelas mesmo sem separador se tiver muitas linhas com pipes
      // Ou se tiver palavras-chave típicas de tabela
      const hasTableKeywords = text.includes('Data') && text.includes('Abertura') && text.includes('Fechamento');

      return (tableLines >= 2 && hasSeparator) || tableLines >= 3 || (tableLines >= 2 && hasTableKeywords);
    };

    // Função para detectar se o texto contém markdown significativo
    const hasMarkdown = (text: string): boolean => {
      // Verificar se tem elementos markdown comuns
      const markdownPatterns = [
        /\*\*.*?\*\*/,     // Bold
        /\*.*?\*/,         // Italic
        /`.*?`/,           // Inline code
        /```[\s\S]*?```/,  // Code blocks
        /^#{1,6}\s/m,      // Headers
        /^\s*[-*+]\s/m,    // Lists
        /^\s*\d+\.\s/m,    // Numbered lists
        /\[.*?\]\(.*?\)/,  // Links
      ];

      return markdownPatterns.some(pattern => pattern.test(text));
    };

    // Função para processar texto com markdown
    const processText = (text: string) => {
      // Primeiro, converter caracteres de escape para quebras de linha reais
      const cleanText = text.replace(/\\n/g, '\n').replace(/\\t/g, '\t');

      if (hasTable(cleanText)) {
        return <TableRenderer content={cleanText} />;
      } else if (hasMarkdown(cleanText)) {
        // Processar markdown
        const htmlContent = marked(cleanText);
        return (
          <div
            className="prose prose-sm max-w-none dark:prose-invert prose-pre:bg-muted prose-pre:text-foreground prose-code:bg-muted prose-code:text-foreground prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-xs"
            dangerouslySetInnerHTML={{ __html: htmlContent }}
          />
        );
      } else {
        // Texto simples
        return <div className="whitespace-pre-wrap">{cleanText}</div>;
      }
    };

    // Renderizar mensagem normal
    return (
      <div
        ref={ref}
        className={`flex ${isMobile ? 'gap-2' : 'gap-3'} ${isUser ? "justify-end" : "justify-start"}`}
      >
        {!isUser && (
          <div className="flex-shrink-0">
            <div className={`bg-primary/10 rounded-full flex items-center justify-center ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`}>
              <Bot className={`text-primary ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
            </div>
          </div>
        )}
        <div
          className={`flex-1 rounded-lg overflow-hidden ${
            isMobile
              ? message.content.echarts
                ? 'max-w-[98%] p-2' // Ainda mais espaço quando há gráfico
                : 'max-w-[85%] p-3'
              : 'max-w-[80%] p-4'
          } ${
            isUser
              ? "bg-primary text-primary-foreground"
              : "bg-muted"
          }`}
        >
          {message.content.text && (
            <div className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
              {processText(message.content.text)}
            </div>
          )}

          {message.content.echarts && (
            <div className={`${isMobile ? 'mt-2' : 'mt-3'} w-full overflow-hidden`}>
              <div className="w-full max-w-full overflow-hidden">
                <EChartsRenderer
                  option={message.content.echarts}
                  height={isMobile ? 320 : 400}
                  className="w-full max-w-full rounded border"
                />
              </div>
            </div>
          )}


        </div>

        {isUser && (
          <div className="flex-shrink-0">
            <div className={`bg-primary rounded-full flex items-center justify-center ${isMobile ? 'h-6 w-6' : 'h-8 w-8'}`}>
              <User className={`text-primary-foreground ${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
            </div>
          </div>
        )}
      </div>
    );
  }
);

ChatMessage.displayName = "ChatMessage";

export default ChatMessage;