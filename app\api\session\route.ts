import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createVertexAIHttpService } from '@/lib/services/vertex-ai-http-service';

// Configuração do ambiente
const CHAT_ENVIRONMENT = process.env.CHAT_ENVIRONMENT || 'local';
const LOCAL_AI_SERVER_URL = process.env.LOCAL_AI_SERVER_URL || 'http://localhost:8000';
const LOCAL_AGENT_NAME = process.env.LOCAL_AGENT_NAME || 'financial_agent';

// Função para criar sessão no ambiente local
async function createLocalSession(userId: string) {
  const endpoint = `${LOCAL_AI_SERVER_URL}/apps/${LOCAL_AGENT_NAME}/users/${userId}/sessions`;
  console.log('Criando nova sessão LOCAL:', endpoint);

  const response = await axios.post(endpoint, {
    additionalProp1: {}
  }, {
    headers: {
      'accept': 'application/json',
      'Content-Type': 'application/json'
    },
    timeout: 10000
  });

  const sessionId = response.data.id || response.data.sessionId;
  if (!sessionId) {
    throw new Error("ID da sessão não retornado pelo servidor local");
  }

  console.log('Sessão LOCAL criada com sucesso:', response.status, 'ID:', sessionId);
  return sessionId;
}

// Função para criar sessão no ambiente de produção (Vertex AI)
async function createProductionSession(userId: string) {
  console.log('Criando nova sessão PRODUÇÃO (Vertex AI)');

  const vertexAIService = createVertexAIHttpService();
  const session = await vertexAIService.createSession(userId);

  console.log('Sessão PRODUÇÃO criada com sucesso:', session.id);
  return session.id;
}

export async function POST(request: NextRequest) {
  try {
    // Obter o corpo da requisição
    const { userId } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: "userId é obrigatório" },
        { status: 400 }
      );
    }

    console.log(`Criando sessão no ambiente: ${CHAT_ENVIRONMENT}`);

    let sessionId: string;

    if (CHAT_ENVIRONMENT === 'production') {
      sessionId = await createProductionSession(userId);
    } else {
      sessionId = await createLocalSession(userId);
    }

    return NextResponse.json({
      success: true,
      sessionId,
      message: "Sessão criada com sucesso",
      environment: CHAT_ENVIRONMENT
    });
  } catch (error: any) {
    console.error('Erro ao criar sessão:', error);

    const environmentName = CHAT_ENVIRONMENT === 'production' ? 'Vertex AI' : 'servidor local';

    if (error.response) {
      return NextResponse.json(
        {
          error: `Erro ao criar sessão no ${environmentName}: ${error.response.status}`,
          message: error.response.data?.message || "Erro no servidor"
        },
        { status: error.response.status }
      );
    }

    return NextResponse.json(
      {
        error: `Erro ao criar sessão no ${environmentName}`,
        message: error.message,
        environment: CHAT_ENVIRONMENT
      },
      { status: 500 }
    );
  }
}

// Função para deletar sessão no ambiente local
async function deleteLocalSession(userId: string, sessionId: string) {
  const endpoint = `${LOCAL_AI_SERVER_URL}/apps/${LOCAL_AGENT_NAME}/users/${userId}/sessions/${sessionId}`;
  console.log('Deletando sessão LOCAL:', endpoint);

  const response = await axios.delete(endpoint, {
    headers: {
      'accept': 'application/json'
    },
    timeout: 10000
  });

  console.log('Sessão LOCAL deletada com sucesso:', response.status);
}

// Função para deletar sessão no ambiente de produção (Vertex AI)
async function deleteProductionSession(userId: string, sessionId: string) {
  console.log('Deletando sessão PRODUÇÃO (Vertex AI)');

  const vertexAIService = createVertexAIHttpService();
  await vertexAIService.deleteSession(userId, sessionId);

  console.log('Sessão PRODUÇÃO deletada com sucesso');
}

export async function DELETE(request: NextRequest) {
  try {
    // Extrair userId e sessionId da URL
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const sessionId = searchParams.get('sessionId');

    if (!userId || !sessionId) {
      return NextResponse.json(
        { error: "userId e sessionId são obrigatórios" },
        { status: 400 }
      );
    }

    console.log(`Deletando sessão no ambiente: ${CHAT_ENVIRONMENT}`);

    if (CHAT_ENVIRONMENT === 'production') {
      await deleteProductionSession(userId, sessionId);
    } else {
      await deleteLocalSession(userId, sessionId);
    }

    return NextResponse.json({
      success: true,
      message: "Sessão deletada com sucesso",
      environment: CHAT_ENVIRONMENT
    });
  } catch (error: any) {
    console.error('Erro ao deletar sessão:', error);

    const environmentName = CHAT_ENVIRONMENT === 'production' ? 'Vertex AI' : 'servidor local';

    if (error.response) {
      return NextResponse.json(
        {
          error: `Erro ao deletar sessão no ${environmentName}: ${error.response.status}`,
          message: error.response.data?.message || "Erro no servidor"
        },
        { status: error.response.status }
      );
    }

    return NextResponse.json(
      {
        error: `Erro ao deletar sessão no ${environmentName}`,
        message: error.message,
        environment: CHAT_ENVIRONMENT
      },
      { status: 500 }
    );
  }
}
