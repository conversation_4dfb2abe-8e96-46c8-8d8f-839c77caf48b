export interface ChatMessage {
  id: string;
  content: {
    text: string;
    echarts?: any; // Dados de configuração do ECharts
  };
  role: "user" | "assistant" | "system";
  timestamp: string;
}

export interface ApiResponsePart {
  text?: string;
  functionCall?: {
    name: string;
    args: {
      [key: string]: any;
    };
  };
  functionResponse?: {
    name: string;
    response: {
      result: string;
    };
  };
}

export interface ApiResponseContent {
  parts: ApiResponsePart[];
  role: "user" | "model";
}

export interface ApiResponse {
  id: string;
  content: ApiResponseContent;
  timestamp: number;
  invocation_id: string;
  author: string;
}