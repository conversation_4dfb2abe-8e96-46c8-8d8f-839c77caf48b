'use client';

import { useMemo } from 'react';
import { Bar, <PERSON><PERSON>hart, CartesianGrid, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

interface MessagesChartProps {
  data: Array<{
    date: string;
    total_tokens: number;
    total_messages: number;
    total_cost: number;
  }>;
  groupBy: string;
}

const chartConfig = {
  total_messages: {
    label: 'Mensagens',
    color: 'hsl(var(--chart-2))',
  },
} satisfies ChartConfig;

export default function MessagesChart({ data, groupBy }: MessagesChartProps) {
  const chartData = useMemo(() => {
    return data.map(item => {
      // A data já vem processada da API no formato YYYY-MM-DD
      const date = new Date(item.date + 'T12:00:00');
      
      let formattedDate: string;

      switch (groupBy) {
        case 'week':
          formattedDate = `${date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}`;
          break;
        case 'month':
          formattedDate = date.toLocaleDateString('pt-BR', { month: 'short', year: 'numeric' });
          break;
        default:
          formattedDate = date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
      }

      return {
        date: formattedDate,
        total_messages: item.total_messages,
      };
    });
  }, [data, groupBy]);

  if (!chartData.length) {
    return (
      <div className="flex items-center justify-center h-[400px] text-muted-foreground">
        Nenhum dado disponível para o período selecionado
      </div>
    );
  }

  return (
    <ChartContainer config={chartConfig} className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis 
            dataKey="date" 
            className="text-xs"
            angle={chartData.length > 7 ? -45 : 0}
            textAnchor={chartData.length > 7 ? 'end' : 'middle'}
            height={chartData.length > 7 ? 60 : 30}
          />
          <YAxis 
            className="text-xs"
          />
          <ChartTooltip 
            content={
              <ChartTooltipContent 
                formatter={(value) => [value, 'Mensagens']}
              />
            }
          />
          <Bar
            dataKey="total_messages"
            fill="hsl(var(--chart-2))"
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  );
}
