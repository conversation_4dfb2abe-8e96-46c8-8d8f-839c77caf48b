import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { createVertexAIHttpService } from '@/lib/services/vertex-ai-http-service';
import { agentCostLogger } from '@/lib/agent-cost-logger';
import { AppConfig, AppUtils } from '@/lib/config/app-config';
import { creditsService } from '@/lib/services/credits-service';

// Configuração do ambiente
const CHAT_ENVIRONMENT = process.env.CHAT_ENVIRONMENT || 'local';
const LOCAL_AI_SERVER_URL = process.env.LOCAL_AI_SERVER_URL || 'http://localhost:8000';
const LOCAL_AGENT_NAME = process.env.LOCAL_AGENT_NAME || 'financial-agent';

// Função para fazer logging das respostas locais
async function logLocalResponse(responseData: any, requestBody: any): Promise<{ success: boolean; remainingCredits: number; error?: string }> {
  try {
    const { user_id, session_id } = requestBody;

    if (!user_id || !session_id) {
      return { success: false, remainingCredits: 0, error: 'user_id ou session_id ausentes' };
    }

    // Verificar se a resposta é um array
    const responses = Array.isArray(responseData) ? responseData : [responseData];

    // Normalizar todas as respostas para o formato esperado pelo logger (NOVA IMPLEMENTAÇÃO)
    const normalizedResponses = responses.map(response => {
      console.log('📝 LOCAL - Analisando resposta:', {
        hasUsageMetadata_snake: !!response.usage_metadata,
        hasUsageMetadata_camel: !!response.usageMetadata,
        hasInvocationId_snake: !!response.invocation_id,
        hasInvocationId_camel: !!response.invocationId,
        hasAuthor: !!response.author,
        role: response.content?.role,
        id: response.id
      });

      // Normalizar os dados para o formato esperado pelo logger (snake_case)
      const usageMetadata = response.usage_metadata || response.usageMetadata;
      const normalizedUsageMetadata = usageMetadata ? {
        candidates_token_count: usageMetadata.candidates_token_count || usageMetadata.candidatesTokenCount || 0,
        prompt_token_count: usageMetadata.prompt_token_count || usageMetadata.promptTokenCount || 0,
        thoughts_token_count: usageMetadata.thoughts_token_count || usageMetadata.thoughtsTokenCount || 0,
        total_token_count: usageMetadata.total_token_count || usageMetadata.totalTokenCount || 0,
        traffic_type: usageMetadata.traffic_type || usageMetadata.trafficType || 'ON_DEMAND'
      } : null;

      const normalizedResponse = {
        ...response,
        usage_metadata: normalizedUsageMetadata,
        invocation_id: response.invocation_id || response.invocationId
      };

      console.log('🔄 LOCAL - Dados normalizados:', {
        original_usageMetadata: !!usageMetadata,
        normalized_usage_metadata: !!normalizedUsageMetadata,
        original_invocationId: response.invocationId,
        normalized_invocation_id: normalizedResponse.invocation_id,
        tokens: normalizedUsageMetadata ? {
          prompt: normalizedUsageMetadata.prompt_token_count,
          candidates: normalizedUsageMetadata.candidates_token_count,
          thoughts: normalizedUsageMetadata.thoughts_token_count
        } : 'N/A'
      });

      return normalizedResponse;
    });

    console.log(`💾 LOCAL - Processando ${normalizedResponses.length} respostas`);

    // Usar a nova função que consome apenas 1 crédito por invocação
    const logResult = await agentCostLogger.logInvocationResponses(
      normalizedResponses,
      user_id,
      session_id
    );

    if (logResult.success) {
      console.log(`✅ LOCAL - Crédito consumido. Restantes: ${logResult.remainingCredits}`);
    } else {
      console.error('❌ LOCAL - Falha ao processar créditos:', logResult.error);
    }

    return logResult;
  } catch (error) {
    console.error('❌ LOCAL - Erro no logging:', error);
    return { success: false, remainingCredits: 0, error: 'Erro no logging' };
  }
}

// Função para processar mensagens no ambiente local (não-streaming)
async function handleLocalEnvironment(requestBody: any) {
  const endpoint = `${LOCAL_AI_SERVER_URL}/run`;

  // Atualizar o app_name para usar a variável de ambiente
  const localRequestBody = {
    ...requestBody,
    app_name: LOCAL_AGENT_NAME
  };

  try {
    const response = await axios.post(endpoint, localRequestBody, {
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json'
      },
      timeout: 120000
    });

    console.log('Resposta LOCAL recebida com status:', response.status);

    // Fazer logging dos custos para ambiente local também
    await logLocalResponse(response.data, requestBody);

    return response.data;
  } catch (error: any) {
    // Tentar com URL alternativa se a primeira falhar com 404
    if (error.response?.status === 404) {
      console.log('Tentando URL alternativa sem /run...');
      const response = await axios.post(LOCAL_AI_SERVER_URL, localRequestBody, {
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });
      console.log('Segunda tentativa LOCAL bem-sucedida!');

      // Fazer logging dos custos para ambiente local também
      await logLocalResponse(response.data, requestBody);

      return response.data;
    }
    throw error;
  }
}



// Função para processar mensagens no ambiente de produção (Vertex AI)
async function handleProductionEnvironment(requestBody: any) {

  const vertexAIService = createVertexAIHttpService();

  const { user_id, session_id, new_message, streaming = false } = requestBody;

  if (!user_id || !session_id || !new_message) {
    throw new Error('Parâmetros obrigatórios ausentes: user_id, session_id, new_message');
  }

  // Extrair o texto da mensagem
  const messageText = new_message.parts?.[0]?.text || '';
  if (!messageText) {
    throw new Error('Texto da mensagem não encontrado');
  }

  // Enviar mensagem para o Vertex AI
  const responses = await vertexAIService.sendMessage(
    user_id,
    session_id,
    messageText,
    streaming
  );

  console.log('Resposta PRODUÇÃO recebida:', responses);
  return responses;
}

// Função para lidar com streaming response
async function handleStreamingResponse(requestBody: any) {
  console.log('Iniciando streaming response');

  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      try {
        if (CHAT_ENVIRONMENT === 'production') {
          // Usar Vertex AI streaming
          const vertexAIService = createVertexAIHttpService();
          const { user_id, session_id, new_message } = requestBody;

          if (!user_id || !session_id || !new_message) {
            throw new Error('Parâmetros obrigatórios ausentes');
          }

          const messageText = new_message.parts?.[0]?.text || '';
          if (!messageText) {
            throw new Error('Texto da mensagem não encontrado');
          }

          // Usar o método de streaming do Vertex AI
          const responses = await vertexAIService.sendMessage(
            user_id,
            session_id,
            messageText,
            true // streaming = true
          );

          // Enviar cada resposta como evento SSE
          for (const response of responses) {
            const data = `data: ${JSON.stringify(response)}\n\n`;
            controller.enqueue(encoder.encode(data));
          }

        } else {
          // Ambiente local - SEM streaming (usar resposta direta)
          const localResponse = await handleLocalEnvironment(requestBody);

          // Converter resposta para formato de streaming
          if (Array.isArray(localResponse)) {
            for (const response of localResponse) {
              const data = `data: ${JSON.stringify(response)}\n\n`;
              controller.enqueue(encoder.encode(data));
            }
          } else {
            const data = `data: ${JSON.stringify(localResponse)}\n\n`;
            controller.enqueue(encoder.encode(data));
          }
        }

        // Enviar evento de finalização
        controller.enqueue(encoder.encode('data: [DONE]\n\n'));
        controller.close();

      } catch (error: any) {
        console.error('Erro no streaming:', error);

        // Enviar erro como resposta
        const errorResponse = {
          id: AppUtils.generateUniqueId('error-stream'),
          content: {
            parts: [{ text: `Erro no streaming: ${error.message || 'Erro desconhecido'}` }],
            role: "model"
          },
          timestamp: Date.now()
        };

        const data = `data: ${JSON.stringify(errorResponse)}\n\n`;
        controller.enqueue(encoder.encode(data));
        controller.close();
      }
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

export async function POST(request: NextRequest) {
  let requestBody: any;

  try {
    // Obter o corpo da requisição
    requestBody = await request.json();

    // Verificar limite de caracteres da mensagem
    const message = requestBody.message || requestBody.new_message?.parts?.[0]?.text || '';

    if (AppUtils.isMessageTooLong(message)) {
      return NextResponse.json(
        [{
          id: AppUtils.generateUniqueId('error-char-limit'),
          content: {
            parts: [{
              text: AppUtils.getCharLimitErrorMessage(message.length)
            }],
            role: "model"
          },
          timestamp: Date.now()
        }],
        { status: 200 }
      );
    }

    // Verificar créditos do usuário antes de processar
    const userId = requestBody.user_id;
    if (userId) {
      const hasCredits = await creditsService.hasCredits(userId, 1);

      if (!hasCredits) {
        const currentCredits = await creditsService.getUserCredits(userId);
        return NextResponse.json(
          [{
            id: AppUtils.generateUniqueId('error-no-credits'),
            content: {
              parts: [{
                text: `❌ **Créditos insuficientes**\n\nVocê tem **${currentCredits} créditos** disponíveis.\n\nPara enviar uma mensagem, você precisa de pelo menos **1 crédito**.\n\nEntre em contato com o suporte para adicionar mais créditos à sua conta.`
              }],
              role: "model"
            },
            timestamp: Date.now()
          }],
          { status: 200 }
        );
      }
    }

    console.log(`Ambiente configurado: ${CHAT_ENVIRONMENT}`);

    // Verificar se é streaming
    const isStreaming = requestBody.streaming === true;

    if (isStreaming) {
      // Retornar streaming response
      return handleStreamingResponse(requestBody);
    } else {
      // Retornar resposta normal
      let responseData;

      if (CHAT_ENVIRONMENT === 'production') {
        responseData = await handleProductionEnvironment(requestBody);
      } else {
        responseData = await handleLocalEnvironment(requestBody);
      }

      return NextResponse.json(responseData);
    }
  } catch (error: any) {
    console.error('Erro ao processar requisição de chat:', error);
    console.error('Detalhes do erro:', {
      message: error.message,
      code: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    // Mensagens de erro mantidas no formato que o cliente espera
    const environmentName = CHAT_ENVIRONMENT === 'production' ? 'Vertex AI' : 'servidor local';

    if (error.code === 'ECONNREFUSED' || error.message === 'Network Error') {
      const message = CHAT_ENVIRONMENT === 'production'
        ? "Não foi possível conectar ao Vertex AI. Verifique as credenciais e configurações."
        : `Não foi possível conectar ao servidor local. Verifique se o servidor está rodando em ${LOCAL_AI_SERVER_URL}`;

      return NextResponse.json(
        [{
          id: AppUtils.generateUniqueId('error-connection'),
          content: {
            parts: [{ text: message }],
            role: "model"
          },
          timestamp: Date.now()
        }],
        { status: 200 }
      );
    }

    if (error.response) {
      const status = error.response.status;
      let message = `O ${environmentName} respondeu com erro: ${status}`;

      if (status === 404) {
        message = CHAT_ENVIRONMENT === 'production'
          ? "Serviço não encontrado no Vertex AI. Verifique a configuração do Reasoning Engine."
          : "Endpoint não encontrado (404). O servidor local está rodando mas o endpoint não foi encontrado.";
      } else if (status === 405) {
        message = `Método não permitido (405). Verifique a configuração do ${environmentName}.`;
      }

      return NextResponse.json(
        [{
          id: AppUtils.generateUniqueId('error-response'),
          content: {
            parts: [{ text: message }],
            role: "model"
          },
          timestamp: Date.now()
        }],
        { status: 200 }
      );
    }

    return NextResponse.json(
      [{
        id: AppUtils.generateUniqueId('error-general'),
        content: {
          parts: [{ text: `Ocorreu um erro na comunicação com o ${environmentName}: ${error.message}` }],
          role: "model"
        },
        timestamp: Date.now()
      }],
      { status: 200 }
    );
  }
}